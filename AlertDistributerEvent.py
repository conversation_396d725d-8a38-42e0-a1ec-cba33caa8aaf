import datetime
import json
import os
from bson import ObjectId
from entities.Notification import Notification, LightAlertZone
from dtos.NotificationDto import NotificationDto, NotificationChannels
from entities.User import User
from entities.Event import Event
from validations.UserPrefsValidation import UserPrefsValidation

class AlertDistributerEvent:
    TOPIC_NAME = os.getenv("NOTIFICATION_TOPIC", "NOTIFICATION_DEV_2")
    def __init__(self, dbhand<PERSON>, producer, appSyncHandler):
        self.producer = producer
        self.dbHandler = dbhandler
        self.appSyncHandler = appSyncHandler
        self.tracked_alertzone = {}
        self.userPrefsValidation = UserPrefsValidation()


    async def disribute_event_notification(self, event_data):
        # Convert the raw event data to an Event object
        event = Event.from_dict(event_data)
        # Use the helper method to get drone location
        drone_location = event.get_drone_location()
        alertZones = self.load_alert_zones(event, drone_location)

        print("alertZones count: ", len(alertZones))
        for alertZone in alertZones:
            print("notification count for alertzone : ", alertZone['organization'][0]['auth0_id'], len(alertZone['notifications']))

            notification_for_organization = self.prepare_drone_detection_data(event, alertZone)
            if event.is_complete():
                await self.appSyncHandler.publish_message(alertZone['organization'][0]['auth0_id'], notification_for_organization)

            if len(alertZone['notifications']) == 0 and not event.is_complete():
                if alertZone['isActive'] == True:
                    await self.appSyncHandler.publish_message(alertZone['organization'][0]['auth0_id'], notification_for_organization)
                await self.prepare_and_save_notification_for_org(alertZone, event, alertZone['orgId'])

                users = self.dbHandler.find_users_for_orgs(str(alertZone['organization'][0]['auth0_id']))
                alertZone['users'] = users
                for user in alertZone['users']:
                    notification = self.prepare_notification_for_user(alertZone, event, alertZone['orgId'], user)
                    if notification.channels.email == True or notification.channels.sms == True:
                        if alertZone['isActive'] == True:
                            await self.producer.produce_message(self.TOPIC_NAME, json.dumps(notification.to_dict()), notification.inAppTopic)


    def load_alert_zones(self, event, drone_location):
        if event.is_complete():
            if event.event_id in self.tracked_alertzone:
                alertZones = self.tracked_alertzone[event.event_id]
                try:
                    self.tracked_alertzone.pop(event.event_id)
                except:
                    print("Error in removing the event from tracked alertzone")
            else:
                self.tracked_alertzone = {}
        else:
            alertZones = self.dbHandler.find_alertZones_with_users_to_send(drone_location, event.event_id)
            if event.event_id not in self.tracked_alertzone:
                self.tracked_alertzone[event.event_id] = alertZones
            else:
                existing_zone_ids = {zone['_id'] for zone in self.tracked_alertzone[event.event_id]}
                new_alerts = [zone for zone in alertZones if zone['_id'] not in existing_zone_ids]
                self.tracked_alertzone[event.event_id].extend(new_alerts)
        return alertZones

    async def prepare_and_save_notification_for_org(self, alertZone, event: Event, orgId: str):
        lightAlertZone = LightAlertZone(_id=alertZone['_id'],
                                        name = alertZone['name'])

        notification = Notification(org_id = ObjectId(orgId),
                                    alertZone = lightAlertZone,
                                    event_id = event.event_id,
                                    timestamp = datetime.datetime.now(datetime.timezone.utc),
                                    seen = False,
                                    type = "ALERT")

        print("saving notification for: ", orgId, "event:", event.event_id)
        try:
            self.dbHandler.save_notification(notification.to_entity())
        except Exception as e:
            print(e)
        return notification

    def prepare_notification_for_user(self, alertZone, event: Event, orgId, user: User):
        isEmailAlert = self.userPrefsValidation.validateEmailAlert(user)
        isSmsAlert  = self.userPrefsValidation.validateSmsAlert(user)
        print("user", user.email,
              "email", isEmailAlert,
              "sms", isSmsAlert)

        notificationChannels = NotificationChannels(email = isEmailAlert,
                                                    sms = isSmsAlert,
                                                    inapp = True)

        notification = NotificationDto(channels = notificationChannels,
                                       templateId = "drone-alert",
                                       subject = "Drone Alert",
                                       organization = orgId,
                                       inAppTopic = user.sub,
                                       phoneNumber = user.phone_number,
                                       email = user.email,
                                       data = self.prepare_drone_detection_data(event, alertZone))

        return notification

    def prepare_drone_detection_data(self, event: Event, alertZone):
        # Use the helper methods from the Event class to get the data

        uas_type = event.get_uas_type()
        location_status = event.get_location_status()
        operator_type = event.get_operator_type()

        # Get operator location
        operator_location = event.get_operator_location()
        operator_lat = operator_location['lat']
        operator_lon = operator_location['lng']

        # Get drone location
        drone_location = event.get_drone_location()

        # Get drone heading
        try:
            drone_heading = event.get_drone_heading()
        except:
            drone_heading = 'N/A'

        return {
            "alertZoneId": alertZone['_id'],
            "alertZoneName": alertZone['name'],
            "currentDate": datetime.datetime.now().strftime("%d/%m/%Y %H:%M:%S"),
            "eventId": event.event_id,
            "uasId": event.uas_id,
            "uasType": uas_type,
            "droneLat": drone_location['lat'],
            "droneLng": drone_location['lng'],
            "pilotLat": operator_lat,
            "pilotLng": operator_lon,
            "locationStatus": location_status,
            "altitude": event.get_drone_altitude(),
            "speed": event.get_drone_speed(),
            "heading": drone_heading,
            "operatorType": operator_type,
            "complete": event.complete,
            "orgName": alertZone['organization'][0]['name'],
            "event": event.to_dict(),
            "envUrl": os.getenv("ENV_URL", "https://dev.aerodefense.tech")
        }
