import json
import os
from common.enums.AlertZoneStatusEnum import AlertZonesStatusEnum
from common.enums.NotificationTypes import NotificationTypeEnum
from entities.SystemNotification import SystemNotification
import datetime
from dtos.NotificationDto import NotificationDto, NotificationChannels
from validations.UserPrefsValidation import UserPrefsValidation
from entities.User import User

class AlertDistributerIoT:
    TOPIC_NAME = os.getenv("NOTIFICATION_TOPIC", "NOTIFICATION_DEV")
    ENVIORNMENT_URL = os.getenv("ENV_URL", "https://dev.aerodefense.tech/")
    def __init__(self,dbhandler, producer, appSyncHandler):
        self.tracked_alertzone={}
        self.producer = producer
        self.dbHandler = dbhandler
        self.appSyncHandler = appSyncHandler
        self.userPrefsValidation  = UserPrefsValidation()

    async def disribute_iot_notification(self, event):
        print("disribute_iot_notification: ", event['service_id'], event['status'])
        alertZones = self.dbHandler.find_alertZones_by_serviceZoneId(event['service_id'])
        print("alertZones count: ",len(alertZones))
        for alertZone in alertZones:
            await self.dbHandler.update_alertZone_status(alertZone['_id'], event['status'])
            notification_data = self.prepare_notification_data(event, alertZone)
            saved_notification = await self.save_notification(notification_data, alertZone['organization'][0]['_id'])
            await self.find_users_and_publish_messages(alertZone, saved_notification, notification_data)

    async def find_users_and_publish_messages(self, alertZone, saved_notification, notification_data):
        org_id = str(alertZone['organization'][0]['auth0_id'])
        await self.appSyncHandler.publish_message(org_id,saved_notification.to_dict())
        users = self.dbHandler.find_users_for_orgs(org_id)
        print("users count", len(users))
        for user in users:
            notification = self.prepare_notification(notification_data, user, org_id)
            if(notification.channels.email == True or notification.channels.sms == True):
                await self.producer.produce_message(self.TOPIC_NAME, json.dumps(notification.to_dict()), notification.inAppTopic)

    def prepare_notification(self, notification_data, user: User, org_id: str):
        isEmailAlert = self.userPrefsValidation.validateEmailAlert(user)
        isSmsAlert  = self.userPrefsValidation.validateSmsAlert(user)
        print("user", user.email,
              "email", isEmailAlert,
              "sms", isSmsAlert,
                                                    )

        notificationChannels = NotificationChannels(email = isEmailAlert,
                                                    sms = isSmsAlert,
                                                    inapp = True)

        notification = NotificationDto(channels = notificationChannels,
                                       templateId = "status-alert",
                                       subject = "Status Alert",
                                       organization = org_id,
                                       inAppTopic = user.sub,
                                       phoneNumber = user.phone_number,
                                       email = user.email,
                                       data = notification_data)
        
        return notification
    
    async def save_notification(self, _notification, orgId):
        message = (
            f"Receiver {_notification['hostname']} "
            f"({_notification['code_version']}) status has been changed to: {_notification['status']}."
        )
        notification_date = datetime.datetime.now(datetime.timezone.utc)
        print("notification_date",notification_date)
        notification = SystemNotification()
        notification.org_id = orgId
        notification.title = "Status Alert"
        notification.description = message
        notification.type = NotificationTypeEnum.STATUS
        notification.metaData['id'] = str(_notification['alertZoneId'])
        notification.metaData['alertZoneName'] = _notification['alertZoneName']
        notification.metaData['status'] = _notification['statusInt']
        notification.metaData['timeStamp'] = str(notification_date)
        notification.seen_by = []
        notification.icon = "status-alert"
        notification.isActive = True
        notification.createdAt = notification_date
        notification.createdBy = "alert-distributor-system"
        saved_notification = await self.dbHandler.save_system_notification(notification.to_entity())
        notification._id = saved_notification.inserted_id
        return notification


    def prepare_notification_data(self, event, alertZone):
        status = AlertZonesStatusEnum(int(event['status'])).name
        notification = {
            'alertZoneName': alertZone['name'],
            'alertZoneId': str(alertZone['_id']),
            'statusInt': int(event['status']),
            'status': status,
            'hostname': event['node_info']['HOSTNAME'],
            'code_version': event['node_info']['CODE_VERSION'],
            'orgName': alertZone['organization'][0]['name'],
            'timeStamp': event['time_stamp'],
            "envUrl": self.ENVIORNMENT_URL
        }
        return notification
