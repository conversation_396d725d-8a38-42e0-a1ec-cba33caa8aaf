"""
Test to verify that the repository refactoring maintains backward compatibility
and that the new repository pattern works correctly.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import asyncio
from bson import ObjectId
import datetime

# Import the classes we want to test
from db.DbHandler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from db.RepositoryManager import RepositoryManager
from db.DatabaseConnection import DatabaseConnection
from db.repositories.SystemNotificationRepository import SystemNotificationRepository
from db.repositories.NotificationRepository import NotificationRepository
from db.repositories.AlertZoneRepository import AlertZoneRepository
from db.repositories.UserRepository import UserRepository

class TestRepositoryRefactoring(unittest.TestCase):
    """Test the repository refactoring to ensure backward compatibility and new functionality."""

    def setUp(self):
        """Set up test fixtures."""
        # Mock the MongoDB connection to avoid actual database calls
        self.mock_client = MagicMock()
        self.mock_db = MagicMock()
        self.mock_collection = MagicMock()

        # Configure the mocks
        self.mock_client.__getitem__.return_value = self.mock_db
        self.mock_db.__getitem__.return_value = self.mock_collection

    @patch('db.DatabaseConnection.MongoClient')
    def test_database_connection_singleton(self, mock_mongo_client):
        """Test that DatabaseConnection is a singleton."""
        mock_mongo_client.return_value = self.mock_client

        # Reset the singleton instance for this test
        DatabaseConnection._instance = None
        DatabaseConnection._client = None

        # Create two instances
        conn1 = DatabaseConnection()
        conn2 = DatabaseConnection()

        # They should be the same instance
        self.assertIs(conn1, conn2)

        # Access the client property to trigger initialization
        _ = conn1.client
        _ = conn2.client

        # MongoClient should only be called once due to singleton pattern
        self.assertEqual(mock_mongo_client.call_count, 1)

    @patch('db.DatabaseConnection.MongoClient')
    def test_db_handler_backward_compatibility(self, mock_mongo_client):
        """Test that DbHandler maintains backward compatibility."""
        mock_mongo_client.return_value = self.mock_client

        # Mock the collection methods
        self.mock_collection.aggregate.return_value = []
        self.mock_collection.insert_one.return_value = Mock(inserted_id=ObjectId())
        self.mock_collection.update_one.return_value = Mock()

        # Create DbHandler instance
        db_handler = DbHandler()

        # Test that all expected attributes exist
        self.assertTrue(hasattr(db_handler, 'alert_zone_collection'))
        self.assertTrue(hasattr(db_handler, 'users_collection'))
        self.assertTrue(hasattr(db_handler, 'notifications_collection'))
        self.assertTrue(hasattr(db_handler, 'system_notifications_collection'))

        # Test that all expected methods exist and are callable
        self.assertTrue(callable(db_handler.find_alertZones_with_users_to_send))
        self.assertTrue(callable(db_handler.find_users_for_orgs))
        self.assertTrue(callable(db_handler.find_alertZones_by_serviceZoneId))
        self.assertTrue(callable(db_handler.save_notification))
        self.assertTrue(callable(db_handler.save_system_notification))
        self.assertTrue(callable(db_handler.update_alertZone_status))

    @patch('db.DatabaseConnection.MongoClient')
    def test_repository_manager_initialization(self, mock_mongo_client):
        """Test that RepositoryManager initializes correctly."""
        mock_mongo_client.return_value = self.mock_client

        repo_manager = RepositoryManager()

        # Test that all repository properties are accessible
        self.assertIsInstance(repo_manager.system_notifications, SystemNotificationRepository)
        self.assertIsInstance(repo_manager.notifications, NotificationRepository)
        self.assertIsInstance(repo_manager.alert_zones, AlertZoneRepository)
        self.assertIsInstance(repo_manager.users, UserRepository)

    @patch('db.DatabaseConnection.MongoClient')
    def test_individual_repositories_initialization(self, mock_mongo_client):
        """Test that individual repositories can be initialized."""
        mock_mongo_client.return_value = self.mock_client

        # Test each repository can be created
        system_repo = SystemNotificationRepository()
        notification_repo = NotificationRepository()
        alert_zone_repo = AlertZoneRepository()
        user_repo = UserRepository()

        # Test that they all have the expected collection attribute
        self.assertTrue(hasattr(system_repo, 'collection'))
        self.assertTrue(hasattr(notification_repo, 'collection'))
        self.assertTrue(hasattr(alert_zone_repo, 'collection'))
        self.assertTrue(hasattr(user_repo, 'collection'))

    @patch('db.DatabaseConnection.MongoClient')
    def test_db_handler_uses_repositories(self, mock_mongo_client):
        """Test that DbHandler methods delegate to repositories."""
        mock_mongo_client.return_value = self.mock_client

        # Mock repository methods
        with patch.object(AlertZoneRepository, 'find_with_users_to_send') as mock_find_alert_zones, \
             patch.object(UserRepository, 'find_for_orgs') as mock_find_users, \
             patch.object(NotificationRepository, 'save') as mock_save_notification:

            mock_find_alert_zones.return_value = []
            mock_find_users.return_value = []
            mock_save_notification.return_value = Mock()

            db_handler = DbHandler()

            # Test that methods delegate to repositories
            point = {"lng": -74.0059, "lat": 40.7128}
            event_id = "test_event"

            result = db_handler.find_alertZones_with_users_to_send(point, event_id)
            mock_find_alert_zones.assert_called_once_with(point, event_id)

            users = db_handler.find_users_for_orgs("test_org")
            mock_find_users.assert_called_once_with("test_org")

            notification = {"test": "data"}
            db_handler.save_notification(notification)
            mock_save_notification.assert_called_once_with(notification)

    @patch('db.DatabaseConnection.MongoClient')
    def test_async_methods_work(self, mock_mongo_client):
        """Test that async methods work correctly."""
        mock_mongo_client.return_value = self.mock_client

        async def run_async_test():
            with patch.object(SystemNotificationRepository, 'save') as mock_save_system, \
                 patch.object(AlertZoneRepository, 'update_status') as mock_update_status:

                mock_save_system.return_value = Mock(inserted_id=ObjectId())
                mock_update_status.return_value = None

                db_handler = DbHandler()

                # Test async system notification save
                notification = Mock()
                result = await db_handler.save_system_notification(notification)
                mock_save_system.assert_called_once_with(notification)

                # Test async alert zone status update
                await db_handler.update_alertZone_status("test_id", "ACTIVE")
                mock_update_status.assert_called_once_with("test_id", "ACTIVE")

        # Run the async test
        asyncio.run(run_async_test())

    @patch('db.DatabaseConnection.MongoClient')
    def test_repository_methods_exist(self, mock_mongo_client):
        """Test that all expected repository methods exist."""
        mock_mongo_client.return_value = self.mock_client

        # Test SystemNotificationRepository methods
        system_repo = SystemNotificationRepository()
        expected_methods = ['save', 'find_by_id', 'find_by_org_id', 'find_active_by_org_id',
                          'mark_as_seen', 'deactivate']
        for method in expected_methods:
            self.assertTrue(hasattr(system_repo, method), f"SystemNotificationRepository missing {method}")

        # Test NotificationRepository methods
        notification_repo = NotificationRepository()
        expected_methods = ['save', 'find_by_id', 'find_by_org_id', 'find_by_event_id',
                          'mark_as_seen', 'delete_by_id', 'find_unseen_by_org_id']
        for method in expected_methods:
            self.assertTrue(hasattr(notification_repo, method), f"NotificationRepository missing {method}")

        # Test AlertZoneRepository methods
        alert_zone_repo = AlertZoneRepository()
        expected_methods = ['find_with_users_to_send', 'find_by_service_zone_id', 'update_status',
                          'find_by_id', 'find_active_by_org_id']
        for method in expected_methods:
            self.assertTrue(hasattr(alert_zone_repo, method), f"AlertZoneRepository missing {method}")

        # Test UserRepository methods
        user_repo = UserRepository()
        expected_methods = ['find_for_orgs', 'find_by_id', 'find_by_email', 'find_by_sub',
                          'find_all_by_org_id', 'save', 'update', 'delete']
        for method in expected_methods:
            self.assertTrue(hasattr(user_repo, method), f"UserRepository missing {method}")

if __name__ == '__main__':
    unittest.main()
