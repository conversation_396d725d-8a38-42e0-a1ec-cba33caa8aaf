from db.DatabaseConnection import DatabaseConnection
from typing import List, Optional
from bson import ObjectId

class AlertZoneRepository:
    """
    Repository class for handling AlertZone entity operations.
    """
    
    def __init__(self):
        self.db_connection = DatabaseConnection()
        self.collection = self.db_connection.get_collection('alertzones')
    
    def find_with_users_to_send(self, point: dict, event_id: str) -> List[dict]:
        """
        Find alert zones with users to send notifications to based on geographic location.
        
        Args:
            point: Dictionary containing 'lng' and 'lat' coordinates
            event_id: String representation of the event ID
            
        Returns:
            List[dict]: List of alert zone documents with user information
        """
        lng = float(point["lng"]) if isinstance(point["lng"], str) else point["lng"]
        lat = float(point["lat"]) if isinstance(point["lat"], str) else point["lat"]
        
        alert_zones = self.collection.aggregate([
            {
                "$geoNear": {
                    "near": {
                        "type": "Point",   
                        "coordinates": [lng, lat]
                    }, 
                    "distanceField": "distance", 
                    "maxDistance": 100, 
                    "spherical": False
                }
            }, 
            {
                '$match': {
                    'isDeleted': False,
                    'isActive': True
                }
            },
            {
                "$lookup": {
                    "from": "notifications",
                    "let": { "alertZoneId": { "$toString": "$_id" } },
                    "pipeline": [
                        {
                            "$match": {
                                "$expr": { 
                                    "$and": [
                                        { 
                                            "$gte": [
                                                "$timestamp", 
                                                { "$subtract": [ { "$toLong": "$$NOW" }, 21600000  ] }
                                            ]
                                        },
                                        { "$eq": ["$event_id", event_id] },
                                        { "$eq": ["$alertZone._id", "$$alertZoneId"] }
                                    ]
                                }
                            }
                        }
                    ],
                    "as": "notifications"
                }
            },
            {
                "$lookup": {
                    "from": "organizations", 
                    "localField": "orgId", 
                    "foreignField": "_id", 
                    "as": "organization"
                }
            },
            {
                "$project": {
                    "organization._id": 0,
                    "notifications._id": 0,
                    "notifications.org_id": 0
                }
            }
        ])
        
        alert_zones = list(alert_zones)
        
        for alertZone in alert_zones:
            alertZone["_id"] = str(alertZone["_id"])
            alertZone["orgId"] = str(alertZone["orgId"])

        return alert_zones
    
    def find_by_service_zone_id(self, service_zone_id: str) -> List[dict]:
        """
        Find alert zones by service zone ID.
        
        Args:
            service_zone_id: String representation of the service zone ID
            
        Returns:
            List[dict]: List of alert zone documents
        """
        alert_zones = self.collection.aggregate([
            {
                '$match': {
                    'isActive': True,
                    'isDeleted': False,
                    'serviceZones': ObjectId(service_zone_id)
                }
            },
            {
                '$lookup': {
                    'from': "organizations",
                    'localField': "orgId",
                    'foreignField': "_id",
                    'as': "organization"
                }
            },
            {
                '$project': {
                    'serviceZones': 0,
                    'geometry': 0,
                    'deletedAt': 0,
                    'createdAt': 0,
                    'updatedAt': 0
                }
            }
        ])
        return list(alert_zones)
    
    async def update_status(self, alert_zone_id: str, status: str):
        """
        Update the status of an alert zone.
        
        Args:
            alert_zone_id: String representation of the alert zone ID
            status: New status to set
        """
        print("updating alertZone status", alert_zone_id, status)
        self.collection.update_one(
            {"_id": ObjectId(alert_zone_id)},
            {"$set": {"latestStatus": status}}
        )
    
    def find_by_id(self, alert_zone_id: str) -> Optional[dict]:
        """
        Find an alert zone by its ID.
        
        Args:
            alert_zone_id: String representation of the alert zone ID
            
        Returns:
            dict or None: The alert zone document if found, None otherwise
        """
        return self.collection.find_one({"_id": ObjectId(alert_zone_id)})
    
    def find_active_by_org_id(self, org_id: str) -> List[dict]:
        """
        Find all active alert zones for a specific organization.
        
        Args:
            org_id: String representation of the organization ID
            
        Returns:
            List[dict]: List of active alert zone documents
        """
        return list(self.collection.find({
            "orgId": ObjectId(org_id),
            "isActive": True,
            "isDeleted": False
        }))
