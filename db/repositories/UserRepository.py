from db.DatabaseConnection import DatabaseConnection
from entities.User import User
from typing import List, Optional
from bson import ObjectId

class UserRepository:
    """
    Repository class for handling User entity operations.
    """
    
    def __init__(self):
        self.db_connection = DatabaseConnection()
        self.collection = self.db_connection.get_collection('users')
        self.user_preferences_collection = self.db_connection.get_collection('userpreferences')
    
    def find_for_orgs(self, orgs: str) -> List[User]:
        """
        Find users for specific organizations.
        
        Args:
            orgs: String representation of organization ID
            
        Returns:
            List[User]: List of User entities
        """
        print("usersForOrgsORG", orgs)
        users = self.collection.aggregate([
            {
                "$match": {
                    "orgs": orgs
                }
            },
            {
                "$lookup": {
                    "from": "userpreferences",
                    "localField": "_id",
                    "foreignField": "user_id",
                    "as": "user_pref"
                }
            }
        ])
        return [User.from_dict(user) for user in users]
    
    def find_by_id(self, user_id: str) -> Optional[dict]:
        """
        Find a user by their ID.
        
        Args:
            user_id: String representation of the user ID
            
        Returns:
            dict or None: The user document if found, None otherwise
        """
        return self.collection.find_one({"_id": ObjectId(user_id)})
    
    def find_by_email(self, email: str) -> Optional[dict]:
        """
        Find a user by their email address.
        
        Args:
            email: Email address to search for
            
        Returns:
            dict or None: The user document if found, None otherwise
        """
        return self.collection.find_one({"email": email})
    
    def find_by_sub(self, sub: str) -> Optional[dict]:
        """
        Find a user by their Auth0 sub identifier.
        
        Args:
            sub: Auth0 sub identifier
            
        Returns:
            dict or None: The user document if found, None otherwise
        """
        return self.collection.find_one({"sub": sub})
    
    def find_all_by_org_id(self, org_id: str) -> List[dict]:
        """
        Find all users belonging to a specific organization.
        
        Args:
            org_id: String representation of the organization ID
            
        Returns:
            List[dict]: List of user documents
        """
        return list(self.collection.find({"org_id": org_id}))
    
    def save(self, user: User):
        """
        Save a user to the database.
        
        Args:
            user: User entity to save
            
        Returns:
            InsertOneResult: Result of the insert operation
        """
        return self.collection.insert_one(user.to_entity())
    
    def update(self, user_id: str, update_data: dict):
        """
        Update a user's information.
        
        Args:
            user_id: String representation of the user ID
            update_data: Dictionary containing fields to update
        """
        self.collection.update_one(
            {"_id": ObjectId(user_id)},
            {"$set": update_data}
        )
    
    def delete(self, user_id: str):
        """
        Delete a user by their ID.
        
        Args:
            user_id: String representation of the user ID
        """
        self.collection.delete_one({"_id": ObjectId(user_id)})
    
    def find_with_preferences(self, user_id: str) -> Optional[dict]:
        """
        Find a user with their preferences included.
        
        Args:
            user_id: String representation of the user ID
            
        Returns:
            dict or None: The user document with preferences if found, None otherwise
        """
        users = list(self.collection.aggregate([
            {
                "$match": {
                    "_id": ObjectId(user_id)
                }
            },
            {
                "$lookup": {
                    "from": "userpreferences",
                    "localField": "_id",
                    "foreignField": "user_id",
                    "as": "user_pref"
                }
            }
        ]))
        return users[0] if users else None
    
    def find_by_service_zones(self, service_zone_ids: List[str]) -> List[dict]:
        """
        Find users by their assigned service zones.
        
        Args:
            service_zone_ids: List of service zone ID strings
            
        Returns:
            List[dict]: List of user documents
        """
        object_ids = [ObjectId(zone_id) for zone_id in service_zone_ids]
        return list(self.collection.find({
            "service_zones": {"$in": object_ids}
        }))
