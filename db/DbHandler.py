from pymongo import MongoClient, GEOSPHERE
import os
from bson import ObjectId
from entities.User import User

class DbHandler:
	MONGODB_URI = os.getenv("MONGODB_CONNECTION_STRING", "mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=CoddnDev")
	MONGODB_DB_NAME = os.getenv("DATABASE_NAME", "coddn")
	client = MongoClient(MONGODB_URI)
	db = client[MONGODB_DB_NAME]
	alert_zone_collection = db["alertzones"]
	users_collection = db["users"]
	notifications_collection = db["notifications"]
	service_zone_collection = db["servicezones"]
	user_prefrences_collection = db["userpreferences"]
	system_notifications_collection = db['system_notifications']

	def find_alertZones_with_users_to_send(self, point, event_id):
		print("finding alertZones", point, event_id)
		lng = float(point["lng"]) if isinstance(point["lng"], str) else point["lng"]
		lat = float(point["lat"]) if isinstance(point["lat"], str) else point["lat"]
		alert_zones = self.alert_zone_collection.aggregate([
			{
				"$geoNear": {
					"near": {
						"type": "Point",   
						"coordinates": [
							lng, lat
						]
					}, 
					"distanceField": "distance", 
					"maxDistance": 100, 
					"spherical": False
				}
			}, 
			{
				'$match': {
					'isDeleted': False,
					'isActive': True
				}
			},
			{
				"$lookup": {
				"from": "notifications",
				"let": { "alertZoneId": { "$toString": "$_id" } },
				"pipeline": [
					{
					"$match": {
						"$expr": { 
							"$and":[
								{ 
							"$gte": [
								"$timestamp", 
								{ "$subtract": [ { "$toLong": "$$NOW" }, 21600000  ] }
									]
								},
								{ "$eq": ["$event_id", event_id] },
								{ "$eq": ["$alertZone._id", "$$alertZoneId"] }
							]
						 }
					}
					}
				],
				"as": "notifications"
				}
			},
			{
				"$lookup": {
					"from": "organizations", 
					"localField": "orgId", 
					"foreignField": "_id", 
					"as": "organization"
				}
			},
			{
		   "$project": {
			"organization._id":0,
			"notifications._id": 0,
			"notifications.org_id": 0
		}
		}
		])
		alert_zones = list(alert_zones)
		
		for alertZone in alert_zones:
			alertZone["_id"] = str(alertZone["_id"])
			alertZone["orgId"] = str(alertZone["orgId"])

		return alert_zones

	def find_users_for_orgs(self, orgs) -> list[User]: 
		print("usersForOrgsORG",orgs)
		users = self.users_collection.aggregate([
		{
			"$match": {
			"orgs": orgs
			}
		},
		{
			"$lookup": {
			"from": "userpreferences",
			"localField": "_id",
			"foreignField": "user_id",
			"as": "user_pref"
			}
		}
		])
		# print("usersForOrgs",list(users))
		return [User.from_dict(user) for user in users]
	
	def find_alertZones_by_serviceZoneId(self, serviceZonesId):
		alert_zones = self.alert_zone_collection.aggregate([
		{
			'$match': {
			'isActive': True,
			'isDeleted': False,
			'serviceZones': ObjectId(serviceZonesId)
			}
		},
		{
			'$lookup': {
			'from': "organizations",
			'localField': "orgId",
			'foreignField': "_id",
			'as': "organization"
			}
		},
		{
			'$project': {
			'serviceZones': 0,
			'geometry': 0,
			'deletedAt': 0,
			'createdAt': 0,
			'updatedAt': 0
			}
		}
		])
		return list(alert_zones)
		
	def save_notification(self, notifcation):
		self.notifications_collection.insert_one(notifcation)

	async def save_system_notification(self, notifcation):
		return self.system_notifications_collection.insert_one(notifcation)
		
		
	async def update_alertZone_status(self, alertZoneId, status):
		print("updating alertZone status", alertZoneId, status)
		self.alert_zone_collection.update_one(
			{"_id": ObjectId(alertZoneId)},
			{"$set": {"latestStatus": status}}
		)