from entities.User import User
from db.repositories.SystemNotificationRepository import SystemNotificationRepository
from db.repositories.NotificationRepository import NotificationRepository
from db.repositories.AlertZoneRepository import AlertZoneRepository
from db.repositories.UserRepository import UserRepository
from db.DatabaseConnection import DatabaseConnection

class DbHandler:
	"""
	Legacy DbHandler class that now uses the repository pattern internally.
	This maintains backward compatibility while providing the new architecture.
	"""

	def __init__(self):
		# Initialize repositories
		self.system_notification_repo = SystemNotificationRepository()
		self.notification_repo = NotificationRepository()
		self.alert_zone_repo = AlertZoneRepository()
		self.user_repo = UserRepository()

		# Maintain legacy collection access for backward compatibility
		self.db_connection = DatabaseConnection()
		self.alert_zone_collection = self.db_connection.get_collection("alertzones")
		self.users_collection = self.db_connection.get_collection("users")
		self.notifications_collection = self.db_connection.get_collection("notifications")
		self.service_zone_collection = self.db_connection.get_collection("servicezones")
		self.user_prefrences_collection = self.db_connection.get_collection("userpreferences")
		self.system_notifications_collection = self.db_connection.get_collection('system_notifications')

	def find_alertZones_with_users_to_send(self, point, event_id):
		"""Use the AlertZoneRepository to find alert zones with users to send notifications to."""
		return self.alert_zone_repo.find_with_users_to_send(point, event_id)

	def find_users_for_orgs(self, orgs) -> list[User]:
		"""Use the UserRepository to find users for organizations."""
		return self.user_repo.find_for_orgs(orgs)

	def find_alertZones_by_serviceZoneId(self, serviceZonesId):
		"""Use the AlertZoneRepository to find alert zones by service zone ID."""
		return self.alert_zone_repo.find_by_service_zone_id(serviceZonesId)

	def save_notification(self, notification):
		"""Use the NotificationRepository to save notifications."""
		return self.notification_repo.save(notification)

	async def save_system_notification(self, notification):
		"""Use the SystemNotificationRepository to save system notifications."""
		return await self.system_notification_repo.save(notification)

	async def update_alertZone_status(self, alertZoneId, status):
		"""Use the AlertZoneRepository to update alert zone status."""
		return await self.alert_zone_repo.update_status(alertZoneId, status)