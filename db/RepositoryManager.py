from db.repositories.SystemNotificationRepository import SystemNotificationRepository
from db.repositories.NotificationRepository import NotificationRepository
from db.repositories.AlertZoneRepository import AlertZoneRepository
from db.repositories.UserRepository import UserRepository
from db.DatabaseConnection import DatabaseConnection

class RepositoryManager:
    """
    Manager class that provides access to all repositories with a single database connection.
    This is the recommended way to access repositories in new code.
    """
    
    def __init__(self):
        # Ensure single database connection across all repositories
        self.db_connection = DatabaseConnection()
        
        # Initialize all repositories
        self._system_notification_repo = None
        self._notification_repo = None
        self._alert_zone_repo = None
        self._user_repo = None
    
    @property
    def system_notifications(self) -> SystemNotificationRepository:
        """Get the SystemNotificationRepository instance."""
        if self._system_notification_repo is None:
            self._system_notification_repo = SystemNotificationRepository()
        return self._system_notification_repo
    
    @property
    def notifications(self) -> NotificationRepository:
        """Get the NotificationRepository instance."""
        if self._notification_repo is None:
            self._notification_repo = NotificationRepository()
        return self._notification_repo
    
    @property
    def alert_zones(self) -> AlertZoneRepository:
        """Get the AlertZoneRepository instance."""
        if self._alert_zone_repo is None:
            self._alert_zone_repo = AlertZoneRepository()
        return self._alert_zone_repo
    
    @property
    def users(self) -> UserRepository:
        """Get the UserRepository instance."""
        if self._user_repo is None:
            self._user_repo = UserRepository()
        return self._user_repo
    
    def close_connection(self):
        """Close the database connection."""
        self.db_connection.close_connection()
    
    def get_collection(self, collection_name: str):
        """
        Get a collection directly from the database.
        Use this for operations not covered by the repositories.
        
        Args:
            collection_name: Name of the collection to retrieve
            
        Returns:
            MongoDB collection object
        """
        return self.db_connection.get_collection(collection_name)
