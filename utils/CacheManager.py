import redis

# Replace with your Valkey endpoint
endpoint = "clustercfg.coddn-test.9nthzl.use2.cache.amazonaws.com"
port = 6379  # Default port

class CacheManager:
    redisClient = redis.Redis(host=endpoint, port=port, ssl=True, decode_responses=True)  # Set decode_responses to True for string handling
    def testConnection(self):
        try:
            pong = self.redisClient.ping()
            print("pong", pong)

        except redis.exceptions.ConnectionError as e:
            print(f"Error connecting to Valkey: {e}")

    def setKey(self, key, value):
        try:
            # Connect to Valkey
            print(f"Successfully connected to Valkey at {endpoint}:{port}")
            self.redisClient.set(key, value, ex=3600) 
            print(f"Set key '{key}' to '{value}'")

        except redis.exceptions.ConnectionError as e:
            print(f"Error connecting to Valkey: {e}")

    def getKey(self, key):
        try:
            value = self.redisClient.get(key)
            print(f"Value of {key}: {value}")
            return value

        except redis.exceptions.ConnectionError as e:
            print(f"Error connecting to Valkey: {e}")
    
    def deleteKey(self, key):
        try:
            self.redisClient.delete(key)
            print(f"Deleted key '{key}'")

        except redis.exceptions.ConnectionError as e:
            print(f"Error connecting to Valkey: {e}")