"""
Example demonstrating how to use the new repository pattern.

This file shows both the legacy DbHandler approach and the new repository approach.
For new code, prefer using RepositoryManager or individual repositories directly.
"""

import asyncio
from db.DbHandler import <PERSON>bHandler
from db.RepositoryManager import RepositoryManager
from entities.SystemNotification import SystemNotification
from entities.Notification import Notification, LightAlertZone
from bson import ObjectId
import datetime

async def legacy_approach_example():
    """Example using the legacy DbHandler (maintains backward compatibility)."""
    print("=== Legacy DbHandler Approach ===")
    
    # Initialize the legacy handler (now uses repositories internally)
    db_handler = DbHandler()
    
    # Find alert zones (same API as before)
    point = {"lng": -74.0059, "lat": 40.7128}  # New York coordinates
    event_id = "test_event_123"
    alert_zones = db_handler.find_alertZones_with_users_to_send(point, event_id)
    print(f"Found {len(alert_zones)} alert zones")
    
    # Find users for organizations
    if alert_zones:
        org_id = alert_zones[0].get('organization', [{}])[0].get('auth0_id', 'test_org')
        users = db_handler.find_users_for_orgs(org_id)
        print(f"Found {len(users)} users for organization")
    
    # Save a notification (same API as before)
    test_notification = {
        "org_id": ObjectId(),
        "alertZone": {"_id": "test_zone", "name": "Test Zone"},
        "event_id": event_id,
        "timestamp": datetime.datetime.now(datetime.timezone.utc),
        "seen": False,
        "type": "ALERT"
    }
    db_handler.save_notification(test_notification)
    print("Saved notification using legacy API")

async def new_repository_approach_example():
    """Example using the new repository pattern (recommended for new code)."""
    print("\n=== New Repository Approach ===")
    
    # Initialize the repository manager
    repo_manager = RepositoryManager()
    
    # Access individual repositories
    alert_zone_repo = repo_manager.alert_zones
    user_repo = repo_manager.users
    notification_repo = repo_manager.notifications
    system_notification_repo = repo_manager.system_notifications
    
    # Find alert zones using the repository
    point = {"lng": -74.0059, "lat": 40.7128}  # New York coordinates
    event_id = "test_event_456"
    alert_zones = alert_zone_repo.find_with_users_to_send(point, event_id)
    print(f"Found {len(alert_zones)} alert zones using repository")
    
    # Find users using the repository
    users = user_repo.find_for_orgs("test_org_id")
    print(f"Found {len(users)} users using repository")
    
    # Save a notification using the repository
    test_notification = {
        "org_id": ObjectId(),
        "alertZone": {"_id": "test_zone", "name": "Test Zone"},
        "event_id": event_id,
        "timestamp": datetime.datetime.now(datetime.timezone.utc),
        "seen": False,
        "type": "ALERT"
    }
    notification_repo.save(test_notification)
    print("Saved notification using repository")
    
    # Create and save a system notification
    system_notification = SystemNotification()
    system_notification.org_id = ObjectId()
    system_notification.title = "Test System Notification"
    system_notification.description = "This is a test notification"
    system_notification.type = None  # You would set the appropriate enum value
    system_notification.seen_by = []
    system_notification.icon = "info"
    system_notification.isActive = True
    system_notification.createdAt = datetime.datetime.now(datetime.timezone.utc)
    system_notification.createdBy = "system"
    system_notification.metaData = {}
    
    result = await system_notification_repo.save(system_notification)
    print(f"Saved system notification with ID: {result.inserted_id}")
    
    # Update alert zone status
    await alert_zone_repo.update_status("test_alert_zone_id", "ACTIVE")
    print("Updated alert zone status")
    
    # Find notifications by organization
    notifications = notification_repo.find_by_org_id(str(ObjectId()))
    print(f"Found {len(notifications)} notifications for organization")
    
    # Close the connection when done
    repo_manager.close_connection()

async def direct_repository_usage_example():
    """Example using repositories directly without the manager."""
    print("\n=== Direct Repository Usage ===")
    
    # Import and use repositories directly
    from db.repositories.UserRepository import UserRepository
    from db.repositories.AlertZoneRepository import AlertZoneRepository
    
    # Create repository instances (they share the same connection via singleton)
    user_repo = UserRepository()
    alert_zone_repo = AlertZoneRepository()
    
    # Use the repositories
    users = user_repo.find_for_orgs("test_org")
    print(f"Found {len(users)} users using direct repository access")
    
    alert_zones = alert_zone_repo.find_by_service_zone_id("test_service_zone")
    print(f"Found {len(alert_zones)} alert zones using direct repository access")

async def main():
    """Run all examples."""
    print("Repository Pattern Examples")
    print("=" * 50)
    
    # Run legacy approach example
    await legacy_approach_example()
    
    # Run new repository approach example
    await new_repository_approach_example()
    
    # Run direct repository usage example
    await direct_repository_usage_example()
    
    print("\n" + "=" * 50)
    print("All examples completed!")

if __name__ == "__main__":
    asyncio.run(main())
